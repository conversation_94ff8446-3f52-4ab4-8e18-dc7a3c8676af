/**
 * Server Components Pattern Implementation
 * 
 * This file demonstrates patterns for future Server Components migration
 * while maintaining compatibility with current client-side rendering.
 */

import React, { Suspense } from 'react';
import { SuspenseBoundary } from '@/components/SuspenseBoundary';

// Type definitions for Server Component patterns
export interface ServerComponentProps {
  children?: React.ReactNode;
  fallback?: React.ReactNode;
}

export interface AsyncComponentProps<T = any> {
  data?: T;
  loading?: boolean;
  error?: Error | null;
}

/**
 * Pattern for data fetching components that can be converted to Server Components
 */
export const DataFetchingPattern = {
  // Client-side data fetching (current implementation)
  client: <T,>(
    Component: React.ComponentType<AsyncComponentProps<T>>,
    fetchData: () => Promise<T>
  ) => {
    return function ClientDataComponent(props: any) {
      const [data, setData] = React.useState<T | undefined>();
      const [loading, setLoading] = React.useState(true);
      const [error, setError] = React.useState<Error | null>(null);

      React.useEffect(() => {
        fetchData()
          .then(setData)
          .catch(setError)
          .finally(() => setLoading(false));
      }, []);

      return <Component {...props} data={data} loading={loading} error={error} />;
    };
  },

  // Server Component pattern (future implementation)
  server: <T,>(
    Component: React.ComponentType<AsyncComponentProps<T>>,
    fetchData: () => Promise<T>
  ) => {
    return async function ServerDataComponent(props: any) {
      try {
        const data = await fetchData();
        return <Component {...props} data={data} loading={false} error={null} />;
      } catch (error) {
        return <Component {...props} data={undefined} loading={false} error={error as Error} />;
      }
    };
  },
};

/**
 * Async Component wrapper for Server Components pattern
 */
export function AsyncComponent<T>({
  children,
  fallback,
  ...props
}: ServerComponentProps & AsyncComponentProps<T>) {
  return (
    <SuspenseBoundary fallback={fallback}>
      {children}
    </SuspenseBoundary>
  );
}

/**
 * Static data component pattern (can be Server Component)
 */
export const StaticDataComponent: React.FC<{
  title: string;
  description: string;
  data: any[];
}> = ({ title, description, data }) => {
  return (
    <div className="space-y-4">
      <div>
        <h2 className="text-2xl font-bold text-white">{title}</h2>
        <p className="text-gray-300">{description}</p>
      </div>
      <div className="grid gap-4">
        {data.map((item, index) => (
          <div key={index} className="p-4 bg-gray-800 rounded-lg">
            {JSON.stringify(item)}
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * Dynamic data component pattern (needs to be Client Component)
 */
export const DynamicDataComponent: React.FC<{
  onUpdate: (data: any) => void;
  interactive?: boolean;
}> = ({ onUpdate, interactive = true }) => {
  const [count, setCount] = React.useState(0);

  const handleClick = () => {
    const newCount = count + 1;
    setCount(newCount);
    onUpdate({ count: newCount });
  };

  if (!interactive) {
    return <div className="text-white">Count: {count}</div>;
  }

  return (
    <button
      onClick={handleClick}
      className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/80 transition-colors"
    >
      Count: {count}
    </button>
  );
};

/**
 * Hybrid component pattern (Server + Client Components)
 */
export const HybridComponent: React.FC<{
  staticData: any[];
  enableInteractions?: boolean;
}> = ({ staticData, enableInteractions = true }) => {
  const handleUpdate = (data: any) => {
    console.log('Updated:', data);
  };

  return (
    <div className="space-y-6">
      {/* This part can be Server Component */}
      <StaticDataComponent
        title="Static Content"
        description="This content can be rendered on the server"
        data={staticData}
      />
      
      {/* This part needs to be Client Component */}
      {enableInteractions && (
        <div className="border-t border-gray-700 pt-6">
          <h3 className="text-lg font-semibold text-white mb-4">Interactive Section</h3>
          <DynamicDataComponent onUpdate={handleUpdate} />
        </div>
      )}
    </div>
  );
};

/**
 * Data streaming pattern for large datasets
 */
export const StreamingDataPattern = {
  // Current implementation with pagination
  paginated: <T,>(
    Component: React.ComponentType<{ items: T[]; hasMore: boolean; onLoadMore: () => void }>,
    fetchPage: (page: number) => Promise<{ items: T[]; hasMore: boolean }>
  ) => {
    return function PaginatedComponent() {
      const [items, setItems] = React.useState<T[]>([]);
      const [page, setPage] = React.useState(0);
      const [hasMore, setHasMore] = React.useState(true);
      const [loading, setLoading] = React.useState(false);

      const loadMore = React.useCallback(async () => {
        if (loading || !hasMore) return;
        
        setLoading(true);
        try {
          const result = await fetchPage(page);
          setItems(prev => [...prev, ...result.items]);
          setHasMore(result.hasMore);
          setPage(prev => prev + 1);
        } catch (error) {
          console.error('Failed to load more:', error);
        } finally {
          setLoading(false);
        }
      }, [page, loading, hasMore]);

      React.useEffect(() => {
        loadMore();
      }, []);

      return <Component items={items} hasMore={hasMore} onLoadMore={loadMore} />;
    };
  },

  // Future streaming implementation
  streaming: <T,>(
    Component: React.ComponentType<{ items: T[] }>,
    streamData: () => AsyncIterable<T[]>
  ) => {
    return async function StreamingComponent() {
      const items: T[] = [];
      
      for await (const chunk of streamData()) {
        items.push(...chunk);
      }
      
      return <Component items={items} />;
    };
  },
};

/**
 * Component composition patterns for Server Components
 */
export const CompositionPatterns = {
  // Layout components (can be Server Components)
  Layout: ({ children }: { children: React.ReactNode }) => (
    <div className="min-h-screen bg-black text-white">
      <header className="border-b border-gray-800 p-4">
        <h1 className="text-xl font-bold">Metamorphic Labs</h1>
      </header>
      <main className="flex-1 p-4">
        {children}
      </main>
      <footer className="border-t border-gray-800 p-4 text-center text-gray-400">
        © 2024 Metamorphic Labs
      </footer>
    </div>
  ),

  // Content components (can be Server Components)
  Content: ({ title, children }: { title: string; children: React.ReactNode }) => (
    <div className="max-w-4xl mx-auto space-y-6">
      <h1 className="text-3xl font-bold text-white">{title}</h1>
      {children}
    </div>
  ),

  // Interactive wrapper (must be Client Component)
  Interactive: ({ children }: { children: React.ReactNode }) => {
    const [mounted, setMounted] = React.useState(false);

    React.useEffect(() => {
      setMounted(true);
    }, []);

    if (!mounted) {
      return <div className="animate-pulse bg-gray-800 h-20 rounded" />;
    }

    return <div className="interactive-wrapper">{children}</div>;
  },
};

export default {
  DataFetchingPattern,
  AsyncComponent,
  StaticDataComponent,
  DynamicDataComponent,
  HybridComponent,
  StreamingDataPattern,
  CompositionPatterns,
};
